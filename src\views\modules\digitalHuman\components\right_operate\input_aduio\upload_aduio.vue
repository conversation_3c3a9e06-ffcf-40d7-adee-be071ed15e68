<template>
    <div class="right_operate_drive_aduio_captions_upload_aduio">
        <div class="right_operate_drive_aduio_captions_upload_aduio_content" v-if="show_aduio_content">
            <aduioContent  ref="aduio_ref" @deleteMusic="deleteMusic"></aduioContent>
            <aduioTextarea ref="textarea_ref"></aduioTextarea>
        </div>
        <div class="right_operate_drive_aduio_captions_upload_aduio_empty" v-else >
            <el-upload
                ref="uploadRef"
                class="upload-demo"
                :on-change="handleFileChange"
                :before-upload="beforeUpload"
                :show-file-list="false"
                accept="audio/*"
                :auto-upload="false"
                >
                <div class="right_operate_drive_aduio_captions_upload_aduio_empty_img">
                    <img src="@/assets/images/digitalHuman/right_operate_drive_aduio_captions_upload_aduio_empty_img.svg" alt="">
                </div>
                <h4>上传音频</h4>
                <span>mp3、wav、m4a格式，最长5分钟，100M以内</span>
            </el-upload>
        </div>
        <div class="right_operate_drive_aduio_captions_upload_aduio_switch">
            <el-switch v-model="open_captions" size="large" style="--el-switch-on-color: #0AAF60; --el-switch-off-color: #F1F2F4" :width="40" active-text="字幕开关"/>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive,defineExpose,nextTick,watch,inject,defineEmits } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useFileUpload } from '@/views/modules/digitalHuman/utils/upload.js';
import aduioContent from '@/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio/aduio.vue';
import aduioTextarea from '@/views/modules/digitalHuman/components/right_operate/input_aduio/upload_aduio/textarea.vue';
import { getDigiAudioJsonTxt } from "@/api/digitalHuman";
import { useloginStore } from '@/stores/login'
let emit=defineEmits(['emit_data'])
let loginStore = useloginStore()
let {  fileChange } = useFileUpload();
let aduio_ref=ref(null)
let textarea_ref=ref(null)
let current_upload_aduio=ref({

})
// 保存新接口返回的详细字幕数据（包含时间信息）
let subtitle_data_with_time=ref([])
let show_aduio_content=ref(false)
let open_aduio=ref(false)
let copy_text=ref("")
let loading = ref(null);
let open_captions=ref(true)  // 🎯 优化：字幕开关默认设置为开启状态


let getUserId = () => {
    return loginStore.userInfo.userId || '0';
};
let handleFileChange=async (file, fileList) => {
    const audio = new Audio(URL.createObjectURL(file.raw))
    audio.addEventListener('loadedmetadata', async() => {
    const duration = audio.duration
    if (duration > 300) { // 300秒 = 5分钟
      ElMessage.error('上传音频时长不能超过5分钟！')
      // 清空上传文件，阻止上传
      uploadRef.value.clearFiles()
    } else {
      // 这里可以手动调用上传，或者其他逻辑
        loading.value=ElLoading.service({
            fullscreen: true,
            lock: true,
            text: '上传中...',
            background: 'rgba(0, 0, 0, 0.7)',
        });
        let uploadRequest = await fileChange(file, fileList);
        console.log(uploadRequest,'uploadRequest');
       
       
        let text = await getDigiAudioJsonTxt({
            url: uploadRequest.url,
            userId: getUserId(),
        });

        if (!text || text.status_code !== 200) {
            ElMessage.error(text?.message || '处理失败');
            loading.value.close();
            return;
        }

        // 处理字幕数据拼接
        let fullText = '';
        if (text.content && text.content.result) {
            // 优先使用 txt 字段
            if (text.content.result.txt) {
                fullText = text.content.result.txt;
            }
            // 从 result 数组中提取 text 字段并拼接（新的数据结构）
            else if (Array.isArray(text.content.result)) {
                // 保存详细的字幕数据（包含时间信息）
                subtitle_data_with_time.value = text.content.result.map(item => ({
                    text: item.text || '',
                    start: item.start || 0,
                    end: item.end || 0,
                    startTime: item.start || 0,  // 兼容字段
                    endTime: item.end || 0       // 兼容字段
                })).filter(item => item.text && item.text.trim() !== '');

                const subtitleTexts = text.content.result
                    .map(item => item.text || '') // 直接使用 text 字段
                    .filter(text => text && text.trim() !== ''); // 过滤空文本

                fullText = subtitleTexts.join(''); // 直接连接，不加空格
            }
            // 从 subtitle_json 数组中提取 text 字段并拼接（兼容旧格式）
            else if (text.content.result.subtitle_json && Array.isArray(text.content.result.subtitle_json)) {
                // 保存详细的字幕数据（兼容旧格式）
                subtitle_data_with_time.value = text.content.result.subtitle_json.map(item => ({
                    text: item.text || '',
                    start: item.start || 0,
                    end: item.end || 0,
                    startTime: item.start || 0,  // 兼容字段
                    endTime: item.end || 0       // 兼容字段
                })).filter(item => item.text && item.text.trim() !== '');

                const subtitleTexts = text.content.result.subtitle_json
                    .map(item => item.text || '') // 直接使用 text 字段
                    .filter(text => text && text.trim() !== ''); // 过滤空文本

                fullText = subtitleTexts.join(''); // 直接连接，不加空格
            }
        }

        if (!fullText) {
            ElMessage.warning('无法提取音频中的文本内容');
        }

        loading.value.close();
        show_aduio_content.value=true
        await nextTick();
        Object.assign(aduio_ref.value.aduio_obj, {
            url: uploadRequest.url,
            try_volume: 80,
            volume: 80,
            isPlaying: false,
            currentTime: 0,
            duration: 0,
            name: uploadRequest.name,
        })
        aduio_ref.value.setupAudio()
        textarea_ref.value.textarea = fullText
    }
  })
   
}
let set_aduio=async(data)=>{
    show_aduio_content.value=true
    await nextTick();
    Object.assign(aduio_ref.value.aduio_obj, {
        url: data.aduio.url,
        try_volume: data.aduio.try_volume,
        volume:data.aduio.volume,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        name:data.aduio.name,
    })
    aduio_ref.value.setupAudio()
    textarea_ref.value.textarea = data.textarea
}
let deleteMusic=()=>{
    current_upload_aduio.value=null
    show_aduio_content.value=false
}
let beforeUpload = (file) => {
   // 允许的音频类型
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/x-wav', 'audio/mp4', 'audio/m4a', 'audio/aac']
  const isAudio = allowedTypes.includes(file.type)
  if (!isAudio) {
    ElMessage.error('上传文件必须是mp3、wav、m4a等音频格式！')
    return false
  }
  const isLt100M = file.size / 1024 / 1024 < 100
  if (!isLt100M) {
    ElMessage.error('上传文件大小不能超过100MB！')
    return false
  }
  // 如果需要限制时长，必须异步检测，beforeUpload不支持异步返回，需在handleFileChange里处理
  return true
};
watch(()=>show_aduio_content,async(newVal)=>{
    await nextTick()
    if(newVal){
         current_upload_aduio.value={
            aduio:aduio_ref.value?.aduio_obj,
            textarea:textarea_ref?.value?.textarea,
            subtitle_data: subtitle_data_with_time.value  // 添加详细的字幕数据
        }
    }

},{immediate:true,deep:true})

// 监听字幕开关状态变化，实时控制字幕显示（音频驱动模式）
watch(open_captions, (newValue) => {
    emit('emit_data')
}, { immediate: false }); // 不立即执行，只在用户操作时触发

defineExpose({
    open_aduio,
    open_captions,
    copy_text,
    current_upload_aduio,
    set_aduio,
    // 🔧 新增：同步字幕开关状态的方法
    syncCaptionsState: (isEnabled) => {
        try {
            open_captions.value = isEnabled;
            
            // 触发数据更新事件，通知父组件状态变化
            emit('emit_data');
        } catch (error) {
            // 同步失败，保持原有状态
        }
    }
})
</script>
<style lang="scss" scoped>
.right_operate_drive_aduio_captions_upload_aduio {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0 18px;
    .right_operate_drive_aduio_captions_upload_aduio_empty{
        box-sizing: border-box;
        overflow: hidden;
        width: 100%;
        border: 1px solid #EFEFF1;
        border-radius: 5px;
        padding: 8px 19px;
        ::v-deep(.upload-demo){
            width: 100%;
            height: 100%;
            .el-upload{
                display: flex;
                flex-direction: column;
                align-items: center;
                .right_operate_drive_aduio_captions_upload_aduio_empty_img{
                    width: 30px;
                    height: 30px;
                    margin-bottom: 6px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                h4{
                    font-weight: normal;
                    font-size: 12px;
                    line-height: 20px;
                    color: #353D49;
                    margin: 0;
                    margin-bottom: 5px;
                }
                span{
                    font-size: 12px;
                    line-height: 20px;
                    color: rgba(0, 0, 0, 0.45);
                }
            }
        }
    }
     .right_operate_drive_aduio_captions_upload_aduio_switch{
            display: flex;
            align-items: center;
            .el-switch{
                display: flex;
                align-items: center;
                height: 44px;
                ::v-deep(.el-switch__core){
                    min-width: auto;
                    height: 20px;
                    .el-switch__action{
                        width: 16px;
                        height: 16px;
                       
                    }
                }
                 ::v-deep(.el-switch__label){
                    margin-left: 8px;
                    height: 22px;
                    span{
                        font-size: 13px;
                        line-height: 22px;
                        color: #303133;
                    }
                    &.is-active{
                        color: #303133;
                    }
                }
                &.is-checked{
                     ::v-deep(.el-switch__core){
                         .el-switch__action{
                            left: unset;
                            right: 2px;
                         }
                    }
                }
            }
        }

}
</style>