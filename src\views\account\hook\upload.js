import { ref } from 'vue';
import { ElMessage } from 'element-plus'; // 确保你已经安装并引入 Element Plus
import { dubbing, callbackOss } from '@/api/dubbing' // 添加这一行
import { useloginStore } from '@/stores/login'
import {userInfo} from '@/api/account.js'
let loginStore = useloginStore()
export function useFileUpload() {
    const isUploading = ref(false);
    const uploadFile = ref({  // 上传文件信息
        name: '',
        size: 0,
        loaded: 0,
        percent: 0,
        type: '',
        url: ''
    });
    const uploadRequest = ref(null);
    const isFileChanging = ref(false);

    const getUserId = () => {
        return loginStore.userInfo.userId || '0';
    };

    // 防抖函数
    const debounce = (func, delay) => {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            
            // 返回一个新的 Promise
            return new Promise((resolve) => {
                timeout = setTimeout(async () => {
                    const lastPromise = func.apply(context, args); // 调用原函数并存储 Promise
                    const result = await lastPromise; // 等待 Promise 完成
                    console.log(result, 666); // 在这里打印结果
                    resolve(result); // 在 Promise 完成后解析
                }, delay);
            });
        };
    };
    let getUserInfo=async()=>{
        return new Promise(async(resolve,reject)=>{
            let user_data=await userInfo({userId:loginStore.userId})
            loginStore.setUserInfo(user_data)
            resolve(true)
        })
        
    }
    // 处理文件上传
    const fileChange = debounce(async (file, fileList) => {
        return new Promise(async (resolve, reject) => {
            console.log(file, 'handleFileChange111');
            if (isFileChanging.value) return; // 防止重复触发
            isFileChanging.value = true;
    
            try {
                // 检查文件类型，只允许图片格式
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
                if (!validImageTypes.includes(file.raw.type)) {
                    ElMessage.error('请上传有效的图片文件（JPEG, PNG, GIF, BMP, WEBP）');
                    isFileChanging.value = false; // 处理完毕，允许再次上传
                    return resolve(uploadFile.value);
                }
    
                // 检查文件大小（最大5MB）
                let maxSize = 5 * 1024 * 1024; // 5MB
                if (file.raw.size > maxSize) {
                    ElMessage.error('文件大小不能超过5MB');
                    isFileChanging.value = false; // 处理完毕，允许再次上传
                    return resolve(uploadFile.value);
                }
    
                isUploading.value = true;
    
                // 设置上传文件信息
                uploadFile.value = {
                    name: file.raw.name,
                    size: file.raw.size,
                    loaded: 0,
                    percent: 0,
                    type: file.raw.type
                };
    
                // 调用dubbing API获取OSS上传凭证
                let response = await dubbing({ userId: getUserId(), fileType: file.raw.type });
    
                // 去掉文件名的后缀
                let fileNameWithoutExt = file.raw.name.split('.').slice(0, -1).join('.');
    
                let formData = new FormData();
                // 添加OSS需要的参数
                formData.append('OSSAccessKeyId', response.accessKeyId);
                formData.append('policy', response.policy);
                formData.append('signature', response.signature);
                formData.append('key', `${response.key.replace(/[^\/]+$/, '')}${file.raw.name}`);
                formData.append('file', file.raw); // 使用file.raw获取原始文件
    
                // 使用XHR上传文件以跟踪进度
                let xhr = new XMLHttpRequest();
                uploadRequest.value = xhr;
    
                // 设置进度监听
                xhr.upload.onprogress = (e) => {
                    if (e.lengthComputable) {
                        uploadFile.value.percent = Math.round(e.loaded / e.total * 100);
                        uploadFile.value.loaded = e.loaded;
                    }
                };
    
                // 上传完成后的处理
                xhr.onload = async () => {
                    try {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            const userId = getUserId();
    
                            // 调用callbackOss接口
                            const callbackResponse = await callbackOss({
                                userId: userId,
                                materialName: fileNameWithoutExt,
                                ossPath: response.key.replace(/[^\/]+$/, '') + file.raw.name,
                                fileSize: String(file.raw.size),
                                fileExtension: file.raw.name.split('.').pop(), // 获取文件扩展名
                                tagNames: '1',
                                materialType: 'image',
                                isPrivate: '1',
                                storage_path: `/material/${userId}/${file.raw.name}`,
                                type:4
                            });
    
                            // 更新文件信息
                            uploadFile.value = {
                                ...uploadFile.value,
                                name: callbackResponse.filename || file.raw.name,
                                url: callbackResponse.url,
                                percent: 100,
                                loaded: file.raw.size
                            };
                            ElMessage.success('图片上传成功');
                            getUserInfo()
                        } else {
                            throw new Error(xhr.statusText || '上传失败');
                        }
                    } catch (error) {
                        console.error('处理错误:', error);
                        ElMessage.error(error.message || '文件处理失败');
                    } finally {
                        console.log(uploadFile.value,777);
                        
                        isFileChanging.value = false;
                        isUploading.value = false;
                        uploadRequest.value = null;
                        resolve(uploadFile.value); // 在这里返回
                    }
                };
    
                // 错误处理
                xhr.onerror = (error) => {
                    console.error('上传错误:', error);
                    ElMessage.error('文件上传失败');
                    isUploading.value = false;
                    uploadFile.value.percent = 0;
                    uploadRequest.value = null;
                    isFileChanging.value = false; // 处理完毕，允许再次上传
                    resolve(uploadFile.value); // 在这里返回
                };
    
                // 发送请求
                xhr.open('POST', response.host, true);
                xhr.send(formData);
    
                // 这里不再直接返回 uploadRequest.value
            } catch (error) {
                console.error('处理错误:', error);
                ElMessage.error('上传准备失败: ' + error.message);
                isUploading.value = false;
                uploadFile.value = {
                    name: '',
                    size: 0,
                    loaded: 0,
                    percent: 0,
                    type: ''
                };
                uploadRequest.value = null;
                isFileChanging.value = false; // 处理完毕，允许再次上传
                resolve(uploadFile.value); // 在这里返回
            }
        });
    }, 300);
    

    return {
        isUploading,
        uploadFile,
        fileChange
    };
}
