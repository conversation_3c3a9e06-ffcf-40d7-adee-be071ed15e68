<template>
    <el-dialog v-model="dialogDetailVisible"  class="ai_synthesis_dialog" width="610px" append-to="#app" :show-close="false">
        <template #header>
            <div class="ai_synthesis_dialog_close" @click="close">
                <img src="@/assets/images/index_images/ai_synthesis_dialog_close.svg" alt="">
            </div>
        </template>
        <template #default>
            <div class="ai_synthesis_dialog_content">
                <div class="ai_synthesis_dialog_content_title">
                    <div class="ai_synthesis_dialog_label">
                        <img src="@/assets/images/index_images/ai_synthesis_dialog_label.svg" alt="">
                    </div>
                    <div class="ai_synthesis_dialog_explain">
                        恭喜您完成首次AI配音体验，扫码添加客服领福利！
                    </div>
                </div>
                <div class="ai_synthesis_dialog_content_ercode">
                    <div class="ai_synthesis_dialog_content_ercode_tip">
                        请扫码领取
                    </div>
                    <div class="ai_synthesis_dialog_content_ercode_img">
                        <!-- <img src="@/assets/images/index_images/ai_synthesis_dialog_content_ercode.svg" alt=""> -->
                         <img src="@/assets/img/duanjunyi.png" alt="">
                    </div>
                    <div class="ai_synthesis_dialog_content_ercode_describe">
                        添加客服获取兑换CDK，前往个人信息-CDK兑换码即可兑换。关闭此页面后，您也可直接点击右上角联系客服领取。
                    </div>
                </div>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref, watch,nextTick,defineExpose,defineProps } from 'vue'
let props = defineProps({
    status: {
        type: [String, Number],
        default: ''
    }
})
let dialogDetailVisible = ref(false)
let close=()=>{
    dialogDetailVisible.value = false
}
let open=async()=>{
    await adjustDialogPosition()
}
let adjustDialogPosition=()=>{
    console.log('adjustDialogPosition');
    
  nextTick(() => {
    const dialogEl = document.querySelector('.ai_synthesis_dialog');
    if (!dialogEl) return;

    const dialogHeight = dialogEl.offsetHeight;
    const windowHeight = window.innerHeight;
    let appHeight = document.getElementById('app').clientHeight;
    let  scale = windowHeight / 953;
    let top=0
    // 计算居中 top，取整避免子像素
    top = Math.round((windowHeight - dialogHeight) / 2);
    if(windowHeight>=953){
        top = Math.round((windowHeight - dialogHeight) / 2);
    }else{
        top = Math.round((windowHeight/(windowHeight/953) - dialogHeight) / 2);
    }
    if(top<0){
        top=0
    }
    console.log('top',windowHeight/(windowHeight/953),dialogHeight,top)
    // 设置 top，取消 transform
    dialogEl.style.top = `${top*scale*(appHeight/953)}px`;
    dialogEl.style.transform = 'none';
    dialogEl.style.transform = `scale(${appHeight/953})`;
    dialogEl.style.margin = '0 auto'; // 保持水平居中
  });
}
watch(() => dialogDetailVisible, (newVal) => {
    if (newVal) {
       open()
    } else {
       close()
    }
}, { immediate: true,deep:true }) 
defineExpose(({
    dialogDetailVisible
}))
</script>
<style lang="scss">
.el-dialog{
    &.ai_synthesis_dialog{
        background-color: transparent;
        background-position: 0 0;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('@/assets/images/index_images/ai_synthesis_dialog_bg.svg');
        height:830px;
        box-shadow: none;
        padding: 0;
        .el-dialog__header {
            padding: 0;
            height: 230px;
            position: relative;
            .ai_synthesis_dialog_close {
                width: 33px;
                height: 33px;
                cursor: pointer;
                position: absolute;
                top: 28px;
                right: 30px;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .el-dialog__body{
            width: 100%;
            display: flex;
            padding: 0;
            padding-left: 117px;
            overflow: hidden;
            .ai_synthesis_dialog_content{
                width: 394px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .ai_synthesis_dialog_content_title{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-bottom: 35px;
                    .ai_synthesis_dialog_label{
                        width: 394px;
                        height: 101px;
                        margin-bottom: 17px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .ai_synthesis_dialog_explain{
                        font-size: 15px;
                        line-height: 20px;
                        color: #FFFFFF;
                    }
                }
                .ai_synthesis_dialog_content_ercode{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .ai_synthesis_dialog_content_ercode_tip{
                        font-size: 15px;
                        line-height: 20px;
                        color: #FFFFFF;
                        margin-bottom: 12px;
                    }
                    .ai_synthesis_dialog_content_ercode_img{
                        width: 163px;
                        height: 164px;
                        padding: 10px;
                        border-radius: 11px;
                        background-color: #fff;
                        box-sizing: border-box;
                        margin-bottom: 21px;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .ai_synthesis_dialog_content_ercode_describe{
                        font-size: 14px;
                        line-height: 20px;
                        text-align: center;
                        color: #FFFFFF;


                    }
                }
                

            }
        }
    }
}
</style>